import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../shared/models/user_profile.dart';
import '../utils/logger.dart';

part 'firestore_service.g.dart';

class FirestoreService {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _auth;

  FirestoreService({
    FirebaseFirestore? firestore,
    FirebaseAuth? auth,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _auth = auth ?? FirebaseAuth.instance;

  /// Get current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  /// Save user profile data to Firestore
  Future<void> saveUserProfile(UserProfile userProfile) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving user profile for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update the profile with current timestamp
      final updatedProfile = userProfile.copyWith(
        updatedAt: DateTime.now(),
      );

      await userDoc.set(updatedProfile.toJson(), SetOptions(merge: true));

      AppLogger.info('FirestoreService: User profile saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving user profile: $e');
      rethrow;
    }
  }

  /// Save user profile data to Firestore without preference fields to avoid duplication
  Future<void> saveUserProfileWithoutPreferences(UserProfile userProfile) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving user profile without preferences for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update the profile with current timestamp
      final updatedProfile = userProfile.copyWith(
        updatedAt: DateTime.now(),
      );

      // Get the full JSON and remove preference fields that would cause duplication
      final profileJson = updatedProfile.toJson();

      // Remove fields that should only exist in preferences or notification_settings
      // Preference fields (should be in users/{userId}/preferences)
      profileJson.remove('activity_level');
      profileJson.remove('allergies');
      profileJson.remove('dietary_restrictions');
      profileJson.remove('health_conditions');
      profileJson.remove('health_goal');
      profileJson.remove('medications');
      profileJson.remove('target_weight');
      profileJson.remove('daily_calorie_goal');
      profileJson.remove('selected_calorie_recommendation');
      profileJson.remove('selected_diet_type');
      profileJson.remove('custom_macro_distribution');
      profileJson.remove('use_custom_macros');
      profileJson.remove('favorite_ingredients');
      profileJson.remove('disliked_ingredients');
      profileJson.remove('favorite_cuisines');
      profileJson.remove('meals_per_day');
      profileJson.remove('snacks_per_day');
      profileJson.remove('daily_burned_calories');
      profileJson.remove('use_manual_calories');

      // Notification fields (should be in users/{userId}/notification_settings)
      profileJson.remove('meal_reminders');
      profileJson.remove('water_reminders');
      profileJson.remove('workout_reminders');
      profileJson.remove('progress_updates');

      await userDoc.set(profileJson, SetOptions(merge: true));

      AppLogger.info('FirestoreService: User profile saved successfully without preferences');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving user profile without preferences: $e');
      rethrow;
    }
  }

  /// Get user profile from Firestore with merged preferences and notification settings
  Future<UserProfile?> getUserProfile([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting user profile for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User profile not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      // Get nested plan preferences and notification settings
      final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
      final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

      // Log the onboarding completion status from Firestore
      AppLogger.info('FirestoreService: onboarding_completed from Firestore: ${data['onboarding_completed']}');

      // Merge plan preferences data into main profile data
      final mergedData = Map<String, dynamic>.from(data);
      if (planPreferences != null) {
        AppLogger.info('FirestoreService: Merging plan preferences: ${planPreferences.keys.toList()}');
        _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
      }
      if (notificationSettings != null) {
        AppLogger.info('FirestoreService: Merging notification settings: ${notificationSettings.keys.toList()}');
        _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
      }

      AppLogger.info('FirestoreService: Final merged data onboarding_completed: ${mergedData['onboarding_completed']}');
      AppLogger.info('FirestoreService: User profile retrieved and merged successfully');

      // Convert Firestore Timestamp objects to DateTime-compatible format
      final convertedData = _convertFirestoreTimestamps(mergedData);
      return UserProfile.fromJson(convertedData);
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting user profile: $e');
      rethrow;
    }
  }

  /// Merge plan preferences data into profile data for UserProfile creation
  void _mergePlanPreferencesIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> planPreferences) {
    // Health Information
    if (planPreferences.containsKey('allergies')) profileData['allergies'] = planPreferences['allergies'];
    if (planPreferences.containsKey('dietary_restrictions')) profileData['dietary_restrictions'] = planPreferences['dietary_restrictions'];
    if (planPreferences.containsKey('health_conditions')) profileData['health_conditions'] = planPreferences['health_conditions'];
    if (planPreferences.containsKey('medications')) profileData['medications'] = planPreferences['medications'];

    // Goals - with enum migration for backward compatibility
    if (planPreferences.containsKey('health_goal')) {
      profileData['health_goal'] = _migrateHealthGoalValue(planPreferences['health_goal']);
    }
    if (planPreferences.containsKey('target_weight')) profileData['target_weight'] = planPreferences['target_weight'];
    if (planPreferences.containsKey('daily_calorie_goal')) profileData['daily_calorie_goal'] = planPreferences['daily_calorie_goal'];
    if (planPreferences.containsKey('selected_calorie_recommendation')) profileData['selected_calorie_recommendation'] = planPreferences['selected_calorie_recommendation'];

    // Diet Type & Macros - with enum migration for backward compatibility
    if (planPreferences.containsKey('selected_diet_type')) {
      profileData['selected_diet_type'] = _migrateDietTypeValue(planPreferences['selected_diet_type']);
    }
    if (planPreferences.containsKey('custom_macro_distribution')) profileData['custom_macro_distribution'] = planPreferences['custom_macro_distribution'];
    if (planPreferences.containsKey('use_custom_macros')) profileData['use_custom_macros'] = planPreferences['use_custom_macros'];

    // Food Preferences
    if (planPreferences.containsKey('favorite_ingredients')) profileData['favorite_ingredients'] = planPreferences['favorite_ingredients'];
    if (planPreferences.containsKey('disliked_ingredients')) profileData['disliked_ingredients'] = planPreferences['disliked_ingredients'];
    if (planPreferences.containsKey('favorite_cuisines')) profileData['favorite_cuisines'] = planPreferences['favorite_cuisines'];
    if (planPreferences.containsKey('meals_per_day')) profileData['meals_per_day'] = planPreferences['meals_per_day'];
    if (planPreferences.containsKey('snacks_per_day')) profileData['snacks_per_day'] = planPreferences['snacks_per_day'];

    // Activity & Physical - with enum migration for backward compatibility
    if (planPreferences.containsKey('activity_level')) {
      profileData['activity_level'] = _migrateActivityLevelValue(planPreferences['activity_level']);
    }
    if (planPreferences.containsKey('daily_burned_calories')) profileData['daily_burned_calories'] = planPreferences['daily_burned_calories'];
    if (planPreferences.containsKey('use_manual_calories')) profileData['use_manual_calories'] = planPreferences['use_manual_calories'];
    if (planPreferences.containsKey('unit_system')) profileData['unit_system'] = planPreferences['unit_system'];
  }

  /// Merge notification settings data into profile data for UserProfile creation
  void _mergeNotificationSettingsIntoProfile(Map<String, dynamic> profileData, Map<String, dynamic> notificationSettings) {
    if (notificationSettings.containsKey('meal_reminders')) profileData['meal_reminders'] = notificationSettings['meal_reminders'];
    if (notificationSettings.containsKey('water_reminders')) profileData['water_reminders'] = notificationSettings['water_reminders'];
    if (notificationSettings.containsKey('workout_reminders')) profileData['workout_reminders'] = notificationSettings['workout_reminders'];
    if (notificationSettings.containsKey('progress_updates')) profileData['progress_updates'] = notificationSettings['progress_updates'];
  }

  /// Update specific fields in user profile
  /// WARNING: This method saves directly to user document root and may cause data duplication.
  /// Consider using saveUserProfileWithoutPreferences, saveUserPreferences, or saveNotificationSettings instead.
  @Deprecated('Use saveUserProfileWithoutPreferences, saveUserPreferences, or saveNotificationSettings to avoid data duplication')
  Future<void> updateUserProfileFields(Map<String, dynamic> fields) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Updating user profile fields for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Add updated timestamp
      fields['updated_at'] = FieldValue.serverTimestamp();

      await userDoc.update(fields);

      AppLogger.info('FirestoreService: User profile fields updated successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error updating user profile fields: $e');
      rethrow;
    }
  }

  /// Save plan preferences to nested structure in Firestore
  Future<void> savePlanPreferences(Map<String, dynamic> planPreferences) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving plan preferences for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update plan preferences with timestamp
      final planPreferencesData = {
        ...planPreferences,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await userDoc.set({
        'plan_preferences': planPreferencesData,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true),);

      AppLogger.info('FirestoreService: Plan preferences saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving plan preferences: $e');
      rethrow;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use savePlanPreferences instead')
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    return await savePlanPreferences(preferences);
  }

  /// Save notification settings to nested structure in Firestore
  Future<void> saveNotificationSettings(Map<String, dynamic> notificationSettings) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving notification settings for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Update notification settings with timestamp
      final notificationData = {
        ...notificationSettings,
        'updated_at': FieldValue.serverTimestamp(),
      };

      await userDoc.set({
        'notification_settings': notificationData,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true),);

      AppLogger.info('FirestoreService: Notification settings saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving notification settings: $e');
      rethrow;
    }
  }

  /// Get plan preferences from nested structure in Firestore
  Future<Map<String, dynamic>?> getPlanPreferences([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting plan preferences for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      return data['plan_preferences'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting plan preferences: $e');
      rethrow;
    }
  }

  /// Legacy method for backward compatibility
  @Deprecated('Use getPlanPreferences instead')
  Future<Map<String, dynamic>?> getUserPreferences([String? userId]) async {
    return await getPlanPreferences(userId);
  }

  /// Get notification settings from nested structure in Firestore
  Future<Map<String, dynamic>?> getNotificationSettings([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting notification settings for $uid');

      final userDoc = await _firestore.collection('users').doc(uid).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      return data['notification_settings'] as Map<String, dynamic>?;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting notification settings: $e');
      rethrow;
    }
  }

  // Weight Tracking Methods

  /// Save individual weight entry to Firestore
  Future<String> saveWeightEntry(Map<String, dynamic> weightEntry) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving weight entry for $userId');

      final weightTrackingCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('weight_tracker');

      // Use the entry ID as the document ID for consistency
      final entryId = weightEntry['id'] as String;
      final docRef = weightTrackingCollectionRef.doc(entryId);

      // Prepare entry data (only the weight entry fields)
      final entryData = {
        'id': weightEntry['id'],
        'weight': weightEntry['weight'],
        'date': weightEntry['date'],
        'created_at': weightEntry['created_at'],
        'updated_at': weightEntry['updated_at'],
        'notes': weightEntry['notes'],
      };

      // Save individual entry to subcollection
      await docRef.set(entryData, SetOptions(merge: true));

      AppLogger.info('FirestoreService: Weight entry saved successfully with ID: $entryId');
      return entryId;
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving weight entry: $e');
      rethrow;
    }
  }

  /// Save weight tracking metadata to user document
  Future<void> saveWeightTrackingMetadata(Map<String, dynamic> metadata) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving weight tracking metadata for $userId');

      final userDoc = _firestore.collection('users').doc(userId);

      // Prepare metadata with timestamps
      final metadataWithTimestamps = {
        ...metadata,
        'synced_at': FieldValue.serverTimestamp(),
        'last_modified': FieldValue.serverTimestamp(),
      };

      // Save metadata to user document under weight_tracker field
      await userDoc.set({
        'weight_tracker': metadataWithTimestamps,
        'updated_at': FieldValue.serverTimestamp(),
      }, SetOptions(merge: true));

      AppLogger.info('FirestoreService: Weight tracking metadata saved successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving weight tracking metadata: $e');
      rethrow;
    }
  }

  /// Get all weight entries for the current user
  Future<List<Map<String, dynamic>>> getWeightEntries() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting weight entries for $userId');

      final weightTrackingCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('weight_tracker');

      // Get all weight entry documents
      final querySnapshot = await weightTrackingCollectionRef
          .orderBy('date', descending: true)
          .get();

      final entries = querySnapshot.docs.map((doc) => doc.data()).toList();

      AppLogger.info('FirestoreService: Found ${entries.length} weight entries');
      return entries;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting weight entries: $e');
      rethrow;
    }
  }

  /// Get weight tracking metadata from user document
  Future<Map<String, dynamic>?> getWeightTrackingMetadata() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting weight tracking metadata for $userId');

      final userDoc = await _firestore.collection('users').doc(userId).get();

      if (!userDoc.exists) {
        AppLogger.info('FirestoreService: User document not found');
        return null;
      }

      final data = userDoc.data();
      if (data == null) {
        AppLogger.warning('FirestoreService: User document exists but has no data');
        return null;
      }

      final weightTrackingData = data['weight_tracker'] as Map<String, dynamic>?;

      if (weightTrackingData != null) {
        AppLogger.info('FirestoreService: Weight tracking metadata found');
        return weightTrackingData;
      }

      AppLogger.info('FirestoreService: No weight tracking metadata found');
      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting weight tracking metadata: $e');
      rethrow;
    }
  }

  /// Delete a specific weight entry from Firestore
  Future<bool> deleteWeightEntry(String entryId) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting weight entry $entryId for $userId');

      final entryDoc = _firestore
          .collection('users')
          .doc(userId)
          .collection('weight_tracker')
          .doc(entryId);

      await entryDoc.delete();

      AppLogger.info('FirestoreService: Weight entry deleted successfully');
      return true;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting weight entry: $e');
      return false;
    }
  }

  /// Delete all weight tracking data from Firestore
  Future<bool> deleteAllWeightTrackingData() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting all weight tracking data for $userId');

      final batch = _firestore.batch();

      // Delete all weight entries from subcollection
      final weightTrackingCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('weight_tracker');

      final querySnapshot = await weightTrackingCollectionRef.get();
      for (final doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      // Delete weight tracking metadata from user document
      final userDoc = _firestore.collection('users').doc(userId);
      batch.update(userDoc, {
        'weight_tracker': FieldValue.delete(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      await batch.commit();

      AppLogger.info('FirestoreService: All weight tracking data deleted successfully');
      return true;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting all weight tracking data: $e');
      return false;
    }
  }

  // Water Intake Tracking Methods

  /// Save daily water intake data to Firestore
  Future<String> saveWaterIntakeData(String date, Map<String, dynamic> waterIntakeData) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Saving water intake data for $userId on $date');

      final waterIntakeCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('water_intake_tracking');

      // Check if document already exists for this date
      final existingDoc = await _getWaterIntakeDocumentForDate(date);

      String documentId;
      if (existingDoc != null) {
        // Update existing document
        documentId = existingDoc.id;
        AppLogger.info('FirestoreService: Updating existing water intake document: $documentId');
      } else {
        // Create new document with auto-generated ID
        final newDocRef = waterIntakeCollectionRef.doc();
        documentId = newDocRef.id;
        AppLogger.info('FirestoreService: Creating new water intake document: $documentId');
      }

      // Prepare data with metadata
      final firestoreData = {
        ...waterIntakeData,
        'date_hash_id': documentId,
        'synced_at': FieldValue.serverTimestamp(),
        'last_modified': FieldValue.serverTimestamp(),
      };

      // Save to Firestore
      await waterIntakeCollectionRef.doc(documentId).set(firestoreData, SetOptions(merge: true));

      AppLogger.info('FirestoreService: Water intake data saved successfully with document ID: $documentId');
      return documentId;
    } catch (e) {
      AppLogger.error('FirestoreService: Error saving water intake data: $e');
      rethrow;
    }
  }

  /// Get water intake data for a specific date
  Future<Map<String, dynamic>?> getWaterIntakeDataForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Getting water intake data for $userId on $date');

      final doc = await _getWaterIntakeDocumentForDate(date);
      if (doc != null && doc.exists) {
        final data = doc.data() as Map<String, dynamic>?;
        if (data != null) {
          AppLogger.info('FirestoreService: Water intake data found for $date');
          return data;
        }
      }

      AppLogger.info('FirestoreService: No water intake data found for $date');
      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting water intake data: $e');
      rethrow;
    }
  }

  /// Get water intake document for a specific date
  Future<DocumentSnapshot?> _getWaterIntakeDocumentForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      final waterIntakeCollectionRef = _firestore
          .collection('users')
          .doc(userId)
          .collection('water_intake_tracking');

      // Query for documents with matching date
      final querySnapshot = await waterIntakeCollectionRef
          .where('date', isEqualTo: date)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first;
      }

      return null;
    } catch (e) {
      AppLogger.error('FirestoreService: Error getting water intake document for date: $e');
      rethrow;
    }
  }

  /// Delete water intake data for a specific date
  Future<bool> deleteWaterIntakeDataForDate(String date) async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting water intake data for $userId on $date');

      final doc = await _getWaterIntakeDocumentForDate(date);
      if (doc != null && doc.exists) {
        await doc.reference.delete();
        AppLogger.info('FirestoreService: Water intake data deleted successfully for $date');
        return true;
      }

      AppLogger.info('FirestoreService: No water intake data found to delete for $date');
      return false;
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting water intake data: $e');
      return false;
    }
  }

  /// Mark onboarding as completed
  Future<void> markOnboardingCompleted() async {
    try {
      final userId = currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Marking onboarding completed for $userId');

      final userDoc = _firestore.collection('users').doc(userId);
      await userDoc.update({
        'onboarding_completed': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      AppLogger.info('FirestoreService: Onboarding marked as completed');
    } catch (e) {
      AppLogger.error('FirestoreService: Error marking onboarding completed: $e');
      rethrow;
    }
  }

  /// Delete user profile
  Future<void> deleteUserProfile([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        throw Exception('User not authenticated');
      }

      AppLogger.info('FirestoreService: Deleting user profile for $uid');

      await _firestore.collection('users').doc(uid).delete();
      
      AppLogger.info('FirestoreService: User profile deleted successfully');
    } catch (e) {
      AppLogger.error('FirestoreService: Error deleting user profile: $e');
      rethrow;
    }
  }

  /// Check if user profile exists
  Future<bool> userProfileExists([String? userId]) async {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        return false;
      }

      final userDoc = await _firestore.collection('users').doc(uid).get();
      return userDoc.exists;
    } catch (e) {
      AppLogger.error('FirestoreService: Error checking if user profile exists: $e');
      return false;
    }
  }

  /// Listen to user profile changes
  Stream<UserProfile?> watchUserProfile([String? userId]) {
    try {
      final uid = userId ?? currentUserId;
      if (uid == null) {
        return Stream.value(null);
      }

      return _firestore
          .collection('users')
          .doc(uid)
          .snapshots()
          .map((snapshot) {
        if (!snapshot.exists || snapshot.data() == null) {
          return null;
        }

        final data = snapshot.data()!;

        // Get nested plan preferences and notification settings
        final planPreferences = data['plan_preferences'] as Map<String, dynamic>?;
        final notificationSettings = data['notification_settings'] as Map<String, dynamic>?;

        // Merge plan preferences data into main profile data
        final mergedData = Map<String, dynamic>.from(data);
        if (planPreferences != null) {
          _mergePlanPreferencesIntoProfile(mergedData, planPreferences);
        }
        if (notificationSettings != null) {
          _mergeNotificationSettingsIntoProfile(mergedData, notificationSettings);
        }

        // Convert Firestore Timestamp objects to DateTime-compatible format
        final convertedData = _convertFirestoreTimestamps(mergedData);
        return UserProfile.fromJson(convertedData);
      });
    } catch (e) {
      AppLogger.error('FirestoreService: Error watching user profile: $e');
      return Stream.error(e);
    }
  }

  /// Migrate old enum name format to new @JsonValue format for HealthGoal
  String _migrateHealthGoalValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'loseWeight':
        return 'lose_weight';
      case 'gainWeight':
        return 'gain_weight';
      case 'buildMuscle':
        return 'build_muscle';
      case 'improveHealth':
        return 'improve_health';
      case 'maintain':
        return 'maintain';
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for DietType
  String? _migrateDietTypeValue(dynamic value) {
    if (value == null) return null;
    if (value is! String) return value.toString();

    switch (value) {
      case 'highProtein':
        return 'high_protein';
      case 'lowCarb':
        return 'low_carb';
      case 'plantBased':
        return 'plant_based';
      case 'balanced':
      case 'ketogenic':
      case 'mediterranean':
      case 'custom':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for ActivityLevel
  String _migrateActivityLevelValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'veryActive':
        return 'very_active';
      case 'sedentary':
      case 'light':
      case 'moderate':
      case 'active':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Migrate old enum name format to new @JsonValue format for Gender
  String _migrateGenderValue(dynamic value) {
    if (value is! String) return value.toString();

    switch (value) {
      case 'notSpecified':
        return 'not_specified';
      case 'male':
      case 'female':
        return value;
      default:
        // If it's already in the correct format or unknown, return as-is
        return value;
    }
  }

  /// Convert Firestore Timestamp objects to DateTime-compatible format for UserProfile
  Map<String, dynamic> _convertFirestoreTimestamps(Map<String, dynamic> data) {
    final convertedData = Map<String, dynamic>.from(data);

    // List of DateTime fields in UserProfile that need conversion
    final dateTimeFields = [
      'createdAt',
      'created_at',
      'updatedAt',
      'updated_at',
      'lastLoginAt',
      'last_login_at',
      'premiumExpiryDate',
      'premium_expiry_date',
    ];

    for (final field in dateTimeFields) {
      if (convertedData.containsKey(field) && convertedData[field] != null) {
        final value = convertedData[field];
        if (value is Timestamp) {
          // Convert Firestore Timestamp to ISO 8601 string format
          convertedData[field] = value.toDate().toIso8601String();
        }
        // If it's already a String or DateTime, leave it as-is
      }
    }

    return convertedData;
  }
}

@riverpod
FirestoreService firestoreService(FirestoreServiceRef ref) {
  return FirestoreService();
}
