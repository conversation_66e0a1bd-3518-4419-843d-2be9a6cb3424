import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../../core/services/storage_service.dart';
import '../../core/services/local_storage_service.dart';
import '../../core/services/firestore_service.dart';
import '../../core/services/sync_service.dart';
import '../../features/water_tracking/data/services/water_intake_sync_service.dart';
import '../../features/weight_tracking/data/services/weight_tracking_sync_service.dart';

import '../../core/utils/logger.dart';
import '../../shared/models/user_profile.dart';
import '../../shared/models/notification_settings.dart';
import '../../shared/models/plan_preferences.dart';
import 'app_state_provider.dart';

part 'auth_provider.freezed.dart';
part 'auth_provider.g.dart';

/// Authentication state
@freezed
class AuthState with _$AuthState {
  const factory AuthState({
    User? user,
    UserProfile? userProfile,
    NotificationSettings? notificationSettings,
    PlanPreferences? planPreferences,
    @Default(AuthStatus.initial) AuthStatus status,
    String? errorMessage,
  }) = _AuthState;
}

/// Authentication status enum
enum AuthStatus {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// Authentication notifier
@riverpod
class AuthNotifier extends _$AuthNotifier {
  late final FirebaseAuth _firebaseAuth;
  late final GoogleSignIn _googleSignIn;
  late final StorageService _storageService;
  late final FirestoreService _firestoreService;

  // Track the path where authentication was initiated to prevent popup navigation issues
  String? _authInitiatedFromPath;

  /// Get the path where authentication was initiated from
  String? get authInitiatedFromPath => _authInitiatedFromPath;

  @override
  AuthState build() {
    _firebaseAuth = FirebaseAuth.instance;
    _googleSignIn = GoogleSignIn(
      // Client ID for web - automatically uses meta tag from index.html
      // For Android, it uses the google-services.json configuration
      scopes: ['email'],
    );
    _storageService = ref.read(storageServiceProvider);
    _firestoreService = ref.read(firestoreServiceProvider);

    AppLogger.info('AuthNotifier: Initializing auth provider');

    // Listen to auth state changes
    _firebaseAuth.authStateChanges().listen((user) {
      AppLogger.info('AuthNotifier: Auth state changed - User: ${user?.uid ?? 'null'}');
      if (user != null) {
        AppLogger.info('AuthNotifier: User authenticated, loading profile');
        // Only load profile if we don't already have this user or if status is not already authenticated
        if (state.user?.uid != user.uid || state.status != AuthStatus.authenticated) {
          _loadUserProfile(user);
        }
        // Clear path tracking on successful authentication
        _authInitiatedFromPath = null;
      } else {
        AppLogger.info('AuthNotifier: User not authenticated');
        // Only update state if we're not in the middle of a loading operation
        if (state.status != AuthStatus.loading) {
          state = const AuthState(status: AuthStatus.unauthenticated);
        }
        // Clear path tracking when user becomes unauthenticated
        _authInitiatedFromPath = null;
      }
    });

    // Check current user on initialization
    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      AppLogger.info('AuthNotifier: Found existing user on init: ${currentUser.uid}');
      // Don't call _loadUserProfile here to avoid race condition
      // The authStateChanges listener will handle it
      return AuthState(
        user: currentUser,
        status: AuthStatus.authenticated,
      );
    }

    AppLogger.info('AuthNotifier: No existing user found');
    return const AuthState(status: AuthStatus.unauthenticated);
  }

  /// Initialize authentication state
  Future<void> initialize() async {
    state = state.copyWith(status: AuthStatus.loading);

    final currentUser = _firebaseAuth.currentUser;
    if (currentUser != null) {
      await _loadUserProfile(currentUser);
    } else {
      state = state.copyWith(status: AuthStatus.unauthenticated);
    }
  }

  /// Load user profile from storage or Firestore
  Future<void> _loadUserProfile(User user) async {
    try {
      AppLogger.info('AuthNotifier: Loading user profile for ${user.uid}');

      UserProfile? userProfile;
      NotificationSettings? notificationSettings;
      PlanPreferences? planPreferences;

      // First, try to load from Firestore (most up-to-date)
      try {
        AppLogger.info('AuthNotifier: Attempting to load profile from Firestore');

        // Get the raw Firestore data (this still returns the merged UserProfile for compatibility)
        final firestoreProfile = await _firestoreService.getUserProfile(user.uid);

        if (firestoreProfile != null) {
          AppLogger.info('AuthNotifier: Found profile in Firestore, parsing into separate models');

          // Parse the merged data into separate models
          userProfile = _extractCoreUserProfile(firestoreProfile);
          notificationSettings = _parseNotificationSettings(firestoreProfile);
          planPreferences = _parsePlanPreferences(firestoreProfile);

          // Cache the separate models locally
          final localStorageService = await ref.read(localStorageServiceProvider.future);

          // Save core user profile
          await localStorageService.saveUserProfile(userProfile.toJson());

          // Save notification settings
          if (notificationSettings != null) {
            await localStorageService.saveNotificationSettings(notificationSettings.toJson());
            AppLogger.info('AuthNotifier: Notification settings parsed and saved to local storage');
          }

          // Save plan preferences
          if (planPreferences != null) {
            await localStorageService.savePlanPreferences(planPreferences.toJson());
            AppLogger.info('AuthNotifier: Plan preferences parsed and saved to local storage');
          }

          // Load current meal plan from Firestore days collection
          try {
            final syncService = await ref.read(syncServiceProvider.future);
            final mealSyncSuccess = await syncService.syncMealPlanFromFirestore();
            if (mealSyncSuccess) {
              AppLogger.info('AuthNotifier: Meal plan data synced from Firestore to local storage');
            } else {
              AppLogger.info('AuthNotifier: No meal plan data found in Firestore or sync failed');
            }
          } catch (e) {
            AppLogger.warning('AuthNotifier: Failed to sync meal plan from Firestore: $e');
            // Don't throw error as this shouldn't break the login flow
          }

          // Load current date's water intake data from Firestore
          try {
            final waterIntakeSyncService = await ref.read(waterIntakeSyncServiceProvider.future);
            await waterIntakeSyncService.validateAndUpdateDateHash();
            final todayWaterIntake = await waterIntakeSyncService.loadTodaysWaterIntakeWithSync();
            if (todayWaterIntake != null) {
              AppLogger.info('AuthNotifier: Water intake data synced from Firestore to local storage');
            } else {
              AppLogger.info('AuthNotifier: No water intake data found for today or sync failed');
            }
          } catch (e) {
            AppLogger.warning('AuthNotifier: Failed to sync water intake from Firestore: $e');
            // Don't throw error as this shouldn't break the login flow
          }

          // Load weight tracking data from Firestore
          try {
            final weightTrackingSyncService = await ref.read(weightTrackingSyncServiceProvider.future);
            final weightSyncSuccess = await weightTrackingSyncService.syncWeightTrackingDataFromFirestore();
            if (weightSyncSuccess) {
              AppLogger.info('AuthNotifier: Weight tracking data synced from Firestore to local storage');
            } else {
              AppLogger.info('AuthNotifier: No weight tracking data found in Firestore or sync failed');
            }
          } catch (e) {
            AppLogger.warning('AuthNotifier: Failed to sync weight tracking data from Firestore: $e');
            // Don't throw error as this shouldn't break the login flow
          }

          // Extract and store onboarding completion status separately for quick access
          await _storageService.setOnboardingCompleted(userProfile.onboardingCompleted);
        }
      } catch (firestoreError) {
        AppLogger.warning('AuthNotifier: Failed to load from Firestore: $firestoreError');
        // Continue to try local storage
      }

      // If no Firestore profile, try local storage
      if (userProfile == null) {
        AppLogger.info('AuthNotifier: Trying to load from local storage');
        final localStorageService = await ref.read(localStorageServiceProvider.future);

        // Load separate models from local storage
        final cachedProfile = localStorageService.loadUserProfile();
        final cachedNotificationSettings = localStorageService.loadNotificationSettings();
        final cachedPlanPreferences = localStorageService.loadPlanPreferences();

        if (cachedProfile != null) {
          AppLogger.info('AuthNotifier: Found cached profile');
          userProfile = UserProfile.fromJson(cachedProfile);

          // Load cached notification settings if available
          if (cachedNotificationSettings != null) {
            notificationSettings = NotificationSettings.fromJson(cachedNotificationSettings);
            AppLogger.info('AuthNotifier: Found cached notification settings');
          }

          // Load cached plan preferences if available
          if (cachedPlanPreferences != null) {
            planPreferences = PlanPreferences.fromJson(cachedPlanPreferences);
            AppLogger.info('AuthNotifier: Found cached plan preferences');
          }

          // Try to sync local profile to Firestore (in background)
          _syncSeparateModelsToFirestore(userProfile, notificationSettings, planPreferences);
        }
      }

      // If still no profile, create a new one
      if (userProfile == null) {
        AppLogger.info('AuthNotifier: Creating new profile from Firebase user');
        userProfile = UserProfile(
          id: user.uid,
          email: user.email ?? '',
          displayName: user.displayName ?? '',
          photoUrl: user.photoURL,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Create default notification settings
        notificationSettings = const NotificationSettings();

        // Plan preferences will be null until onboarding is completed

        // Cache the new profile locally using regular local storage
        final localStorageService = await ref.read(localStorageServiceProvider.future);
        await localStorageService.saveUserProfile(userProfile.toJson());
        await localStorageService.saveNotificationSettings(notificationSettings.toJson());

        // Extract and store onboarding completion status separately for quick access
        await _storageService.setOnboardingCompleted(userProfile.onboardingCompleted);

        // Save new profile to Firestore (in background)
        _syncSeparateModelsToFirestore(userProfile, notificationSettings, planPreferences);

        AppLogger.info('AuthNotifier: New profile created and cached');
      }

      AppLogger.info('AuthNotifier: Setting auth state to authenticated');
      AppLogger.info('AuthNotifier: Profile onboardingCompleted status: ${userProfile.onboardingCompleted}');

      // Clear guest user status for authenticated non-anonymous users
      if (!user.isAnonymous) {
        AppLogger.info('AuthNotifier: User is not anonymous, clearing guest user status');
        try {
          // Clear the is_guest_user flag from storage
          await _storageService.remove('is_guest_user');

          // Update app state to reflect user is no longer a guest
          await ref.read(appStateNotifierProvider.notifier).setGuestUser(false);

          AppLogger.info('AuthNotifier: Guest user status cleared successfully');
        } catch (e) {
          AppLogger.warning('AuthNotifier: Failed to clear guest user status: $e');
          // Don't throw error as this shouldn't break the login flow
        }
      }

      state = state.copyWith(
        user: user,
        userProfile: userProfile,
        notificationSettings: notificationSettings,
        planPreferences: planPreferences,
        status: AuthStatus.authenticated,
        errorMessage: null,
      );

      // Sync local app state if user has completed onboarding
      if (userProfile.onboardingCompleted) {
        AppLogger.info('AuthNotifier: User has completed onboarding, syncing local app state');
        try {
          final currentAppState = ref.read(appStateNotifierProvider);
          AppLogger.info('AuthNotifier: Current app state onboarding completed: ${currentAppState.isOnboardingCompleted}');

          if (!currentAppState.isOnboardingCompleted) {
            AppLogger.info('AuthNotifier: Marking onboarding as completed in app state');
            await ref.read(appStateNotifierProvider.notifier).completeOnboarding();
          }
        } catch (e) {
          AppLogger.warning('AuthNotifier: Failed to sync local app state: $e');
          // Don't throw error as this shouldn't break the login flow
        }

        // Sync Firestore data to local storage after successful login
        await _syncFirestoreDataToLocalStorage();
      } else {
        AppLogger.info('AuthNotifier: User has NOT completed onboarding, skipping local app state sync');
      }

      AppLogger.info('AuthNotifier: Auth state updated - Status: ${state.status}');
    } catch (e) {
      AppLogger.warning('AuthNotifier: Error loading user profile: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: e.toString(),
      );
    }
  }



  /// Sign in with email and password
  Future<void> signInWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign up with email and password
  Future<void> signUpWithEmailAndPassword(String email, String password) async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign in with Google
  Future<void> signInWithGoogle([String? currentPath]) async {
    try {
      AppLogger.info('AuthNotifier: Starting Google Sign-In from path: ${currentPath ?? 'unknown'}');

      // Track where authentication was initiated from
      _authInitiatedFromPath = currentPath;

      // Set loading state but preserve current user if exists to prevent router confusion
      final currentUser = state.user;
      state = state.copyWith(status: AuthStatus.loading, user: currentUser);

      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        AppLogger.info('AuthNotifier: Google Sign-In cancelled by user');
        _authInitiatedFromPath = null; // Clear path tracking on cancellation
        // Restore previous state instead of setting unauthenticated
        state = state.copyWith(
          status: currentUser != null ? AuthStatus.authenticated : AuthStatus.unauthenticated,
          user: currentUser,
        );
        return;
      }

      AppLogger.info('AuthNotifier: Google user obtained: ${googleUser.email}');
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      AppLogger.info('AuthNotifier: Signing in with Firebase credential');
      final userCredential = await _firebaseAuth.signInWithCredential(credential);

      if (userCredential.user != null) {
        AppLogger.info('AuthNotifier: Firebase sign-in successful: ${userCredential.user!.uid}');
        // The authStateChanges listener will handle the state update
        // Don't manually set state here to prevent race conditions
      } else {
        AppLogger.warning('AuthNotifier: Firebase sign-in returned null user');
        _authInitiatedFromPath = null; // Clear path tracking on error
        state = state.copyWith(
          status: AuthStatus.error,
          errorMessage: 'Sign-in failed',
          user: currentUser, // Preserve previous user state
        );
      }
    } on FirebaseAuthException catch (e) {
      AppLogger.warning('AuthNotifier: Firebase auth error: ${e.code} - ${e.message}');
      _authInitiatedFromPath = null; // Clear path tracking on error
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
        user: state.user, // Preserve current user state
      );
    } catch (e) {
      AppLogger.warning('AuthNotifier: Unexpected error during Google Sign-In: $e');
      _authInitiatedFromPath = null; // Clear path tracking on error
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred: $e',
        user: state.user, // Preserve current user state
      );
    }
  }

  /// Sign in as guest
  Future<void> signInAsGuest() async {
    try {
      state = state.copyWith(status: AuthStatus.loading);

      final credential = await _firebaseAuth.signInAnonymously();

      if (credential.user != null) {
        await _loadUserProfile(credential.user!);
      }
    } on FirebaseAuthException catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: _getErrorMessage(e.code),
      );
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'An unexpected error occurred',
      );
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _firebaseAuth.signOut();
      await _googleSignIn.signOut();
      await _storageService.clearUserData();

      // Also clear user data from local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.clearUserData();

      state = const AuthState(status: AuthStatus.unauthenticated);
    } catch (e) {
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to sign out',
      );
    }
  }

  /// Update user profile using new Firestore structure
  Future<void> updateUserProfile(UserProfile updatedProfile, {bool isOnboardingCompletion = false}) async {
    try {
      // Update local state
      state = state.copyWith(userProfile: updatedProfile);

      // Cache updated profile locally using regular local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveUserProfile(updatedProfile.toJson());

      // Extract and store onboarding completion status separately for quick access
      await _storageService.setOnboardingCompleted(updatedProfile.onboardingCompleted);

      // Sync to Firestore using new structure (in background)
      _syncSeparateModelsToFirestore(updatedProfile, state.notificationSettings, state.planPreferences);

      AppLogger.info('AuthNotifier: User profile updated successfully using new structure (onboarding: $isOnboardingCompletion)');
    } catch (e) {
      AppLogger.error('AuthNotifier: Failed to update profile: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to update profile',
      );
    }
  }

  /// Update notification settings
  Future<void> updateNotificationSettings(NotificationSettings updatedSettings) async {
    try {
      // Update local state
      state = state.copyWith(notificationSettings: updatedSettings);

      // Cache updated settings locally
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveNotificationSettings(updatedSettings.toJson());

      // Sync to Firestore (in background)
      if (state.userProfile != null) {
        _syncSeparateModelsToFirestore(state.userProfile!, updatedSettings, state.planPreferences);
      }

      AppLogger.info('AuthNotifier: Notification settings updated successfully');
    } catch (e) {
      AppLogger.error('AuthNotifier: Failed to update notification settings: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to update notification settings',
      );
    }
  }

  /// Update plan preferences
  Future<void> updatePlanPreferences(PlanPreferences updatedPreferences) async {
    try {
      // Update local state
      state = state.copyWith(planPreferences: updatedPreferences);

      // Cache updated preferences locally
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.savePlanPreferences(updatedPreferences.toJson());

      // Sync to Firestore (in background)
      if (state.userProfile != null) {
        _syncSeparateModelsToFirestore(state.userProfile!, state.notificationSettings, updatedPreferences);
      }

      AppLogger.info('AuthNotifier: Plan preferences updated successfully');
    } catch (e) {
      AppLogger.error('AuthNotifier: Failed to update plan preferences: $e');
      state = state.copyWith(
        status: AuthStatus.error,
        errorMessage: 'Failed to update plan preferences',
      );
    }
  }

  /// Sync Firestore data to local storage after login
  Future<void> _syncFirestoreDataToLocalStorage() async {
    try {
      AppLogger.info('AuthNotifier: Syncing Firestore data to local storage');

      // Load plan preferences from Firestore
      final planPreferences = await _firestoreService.getPlanPreferences();
      if (planPreferences != null) {
        // Convert any Timestamp objects to ISO strings for JSON serialization
        final cleanedPreferences = _convertTimestampsToStrings(planPreferences);
        await _storageService.setObject('plan_preferences', cleanedPreferences);
        AppLogger.info('AuthNotifier: Plan preferences synced to local storage');
      }

      // Load notification settings from Firestore
      final notificationSettings = await _firestoreService.getNotificationSettings();
      if (notificationSettings != null) {
        // Convert any Timestamp objects to ISO strings for JSON serialization
        final cleanedSettings = _convertTimestampsToStrings(notificationSettings);
        await _storageService.setObject('notification_settings', cleanedSettings);
        AppLogger.info('AuthNotifier: Notification settings synced to local storage');
      }

      AppLogger.info('AuthNotifier: Firestore data sync to local storage completed');
    } catch (e) {
      AppLogger.warning('AuthNotifier: Failed to sync Firestore data to local storage: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }

  /// Convert Firestore Timestamp objects to ISO strings for JSON serialization
  Map<String, dynamic> _convertTimestampsToStrings(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is Timestamp) {
        // Convert Timestamp to ISO string
        result[key] = value.toDate().toIso8601String();
      } else if (value is Map<String, dynamic>) {
        // Recursively convert nested maps
        result[key] = _convertTimestampsToStrings(value);
      } else if (value is List) {
        // Handle lists that might contain maps with timestamps
        result[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertTimestampsToStrings(item);
          } else if (item is Timestamp) {
            return item.toDate().toIso8601String();
          }
          return item;
        }).toList();
      } else {
        // Keep other values as-is
        result[key] = value;
      }
    }

    return result;
  }

  /// Manually sync Firestore data after login
  /// This can be called from login widgets to ensure data is synced
  /// Note: This is now mainly for manual refresh scenarios since automatic sync
  /// happens in _loadUserProfile during authentication
  Future<void> syncFirestoreData() async {
    try {
      final user = state.user;
      if (user == null) {
        AppLogger.warning('AuthNotifier: Cannot sync Firestore data - no authenticated user');
        return;
      }

      AppLogger.info('AuthNotifier: Manually syncing Firestore data for ${user.uid}');

      // Reload the user profile to get the latest data
      await _loadUserProfile(user);

      AppLogger.info('AuthNotifier: Manual Firestore data sync completed');
    } catch (e) {
      AppLogger.error('AuthNotifier: Error syncing Firestore data: $e');
      // Don't throw error as this shouldn't break the login flow
    }
  }

  /// Reset password
  Future<void> resetPassword(String email) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on FirebaseAuthException catch (e) {
      throw _getErrorMessage(e.code);
    }
  }

  /// Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }



  /// Extract core user profile from the merged Firestore UserProfile
  UserProfile _extractCoreUserProfile(UserProfile firestoreProfile) {
    return UserProfile(
      id: firestoreProfile.id,
      email: firestoreProfile.email,
      displayName: firestoreProfile.displayName,
      photoUrl: firestoreProfile.photoUrl,
      phoneNumber: firestoreProfile.phoneNumber,
      preferredLanguage: firestoreProfile.preferredLanguage,
      unitSystem: firestoreProfile.unitSystem,
      isPremium: firestoreProfile.isPremium,
      onboardingCompleted: firestoreProfile.onboardingCompleted,
      createdAt: firestoreProfile.createdAt,
      updatedAt: firestoreProfile.updatedAt,
      lastLoginAt: firestoreProfile.lastLoginAt,
      premiumExpiryDate: firestoreProfile.premiumExpiryDate,
    );
  }

  /// Parse notification settings from the merged Firestore UserProfile
  NotificationSettings? _parseNotificationSettings(UserProfile firestoreProfile) {
    // The old UserProfile model had these fields, but they're not in the new one
    // We need to extract them from the raw Firestore data if available
    // For now, return default settings - this will be improved when we update the Firestore service
    return const NotificationSettings();
  }

  /// Parse plan preferences from the merged Firestore UserProfile
  PlanPreferences? _parsePlanPreferences(UserProfile firestoreProfile) {
    // The old UserProfile model had these fields, but they're not in the new one
    // We need to extract them from the raw Firestore data if available
    // For now, return null - this will be improved when we update the Firestore service
    return null;
  }

  /// Sync separate models to Firestore in background
  void _syncSeparateModelsToFirestore(
    UserProfile userProfile,
    NotificationSettings? notificationSettings,
    PlanPreferences? planPreferences,
  ) {
    // Run in background without blocking the UI
    Future.microtask(() async {
      try {
        AppLogger.info('AuthNotifier: Syncing separate models to Firestore in background');

        // Save the core user profile
        await _firestoreService.saveUserProfile(userProfile);

        // Save notification settings if available
        if (notificationSettings != null) {
          await _firestoreService.saveNotificationSettings(notificationSettings.toJson());
        }

        // Save plan preferences if available
        if (planPreferences != null) {
          await _firestoreService.savePlanPreferences(planPreferences.toJson());
        }

        AppLogger.info('AuthNotifier: Separate models synced to Firestore successfully');
      } catch (e) {
        AppLogger.warning('AuthNotifier: Failed to sync separate models to Firestore: $e');
        // Don't throw error as this is a background operation
      }
    });
  }

  /// Get user-friendly error message
  String _getErrorMessage(String code) {
    switch (code) {
      case 'user-not-found':
        return 'No user found with this email address.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'email-already-in-use':
        return 'An account already exists with this email address.';
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'This operation is not allowed.';
      default:
        return 'An error occurred. Please try again.';
    }
  }
}

/// Provider for current user
@riverpod
User? currentUser(CurrentUserRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.user;
}

/// Provider for current user profile
@riverpod
UserProfile? currentUserProfile(CurrentUserProfileRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.userProfile;
}

/// Provider for current notification settings
@riverpod
NotificationSettings? currentNotificationSettings(CurrentNotificationSettingsRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.notificationSettings;
}

/// Provider for current plan preferences
@riverpod
PlanPreferences? currentPlanPreferences(CurrentPlanPreferencesRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.planPreferences;
}

/// Provider for authentication status
@riverpod
AuthStatus authStatus(AuthStatusRef ref) {
  final authState = ref.watch(authNotifierProvider);
  return authState.status;
}

/// Provider for checking if user is authenticated
@riverpod
bool isAuthenticated(IsAuthenticatedRef ref) {
  final authState = ref.watch(authNotifierProvider);
  final isAuth = authState.status == AuthStatus.authenticated;
  AppLogger.info('isAuthenticated: Status=${authState.status}, User=${authState.user?.uid ?? 'null'}, Result=$isAuth');
  return isAuth;
}

/// Provider for checking if user is guest (disabled for demo)
// @riverpod
// bool isGuest(IsGuestRef ref) {
//   final user = ref.watch(currentUserProvider);
//   return user?.isAnonymous ?? false;
// }
