import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/local_storage_service.dart';
import '../../../../core/utils/logger.dart';
import '../../data/models/weight_tracking_models.dart';
import '../../data/services/weight_tracking_sync_service.dart';

part 'weight_tracking_provider.freezed.dart';
part 'weight_tracking_provider.g.dart';

@freezed
class WeightTrackingState with _$WeightTrackingState {
  const factory WeightTrackingState({
    @Default(WeightTrackingData()) WeightTrackingData data,
    @Default(false) bool isLoading,
    @Default(false) bool isSaving,
    String? error,
  }) = _WeightTrackingState;
}

@riverpod
class WeightTrackingNotifier extends _$WeightTrackingNotifier {
  @override
  WeightTrackingState build() {
    // Load data on initialization
    _loadWeightTrackingData();
    return const WeightTrackingState();
  }

  /// Load weight tracking data from local storage
  Future<void> _loadWeightTrackingData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      
      // Load weight data
      final weightData = localStorageService.loadWeightTrackingData();
      
      // Load settings
      final settingsData = localStorageService.loadWeightSettings();
      
      WeightTrackingData data;
      if (weightData != null) {
        data = WeightTrackingData.fromJson(weightData);
      } else {
        data = const WeightTrackingData();
      }
      
      // Apply settings if available
      if (settingsData != null) {
        final settings = WeightTrackingSettings.fromJson(settingsData);
        data = data.copyWith(settings: settings);
      }
      
      // Update computed properties
      final latestEntry = data.latestEntry;
      data = data.copyWith(
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: data.chronologicalEntries.isNotEmpty 
            ? data.chronologicalEntries.first.weight 
            : null,
      );

      state = state.copyWith(
        isLoading: false,
        data: data,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحميل بيانات الوزن: $e',
      );
    }
  }

  /// Reload weight tracking data from local storage (public method)
  Future<void> reloadWeightTrackingData() async {
    await _loadWeightTrackingData();
  }

  /// Add a new weight entry
  Future<void> addWeightEntry({
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      // Validate the entry
      final validation = WeightValidator.validateWeightEntry(
        weight: weight,
        date: date,
        existingEntries: state.data.entries,
        isEditing: false,
      );

      if (!validation.isValid) {
        throw Exception(validation.errorMessage);
      }

      // Create new entry
      final newEntry = WeightEntry(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        weight: weight,
        date: date,
        createdAt: DateTime.now(),
        notes: notes,
      );

      // Add to existing entries
      final updatedEntries = [...state.data.entries, newEntry];
      
      // Update data
      final updatedData = state.data.copyWith(
        entries: updatedEntries,
        lastEntryDate: date,
        currentWeight: weight,
        startingWeight: state.data.startingWeight ?? weight, // Set starting weight if first entry
      );

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
      );

      // Save to local storage
      await _saveWeightTrackingData(updatedData);

      // Sync the new entry to Firestore in background
      _syncWeightEntryToFirestoreInBackground(newEntry);

      // Show warnings if any
      if (validation.warnings.isNotEmpty) {
        // Note: In a real app, you might want to show these warnings in the UI
        // For now, we'll just log them or handle them in the UI layer
      }
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في إضافة الوزن: $e',
      );
    }
  }

  /// Edit an existing weight entry
  Future<void> editWeightEntry({
    required String entryId,
    required double weight,
    required DateTime date,
    String? notes,
  }) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      // Validate the entry
      final validation = WeightValidator.validateWeightEntry(
        weight: weight,
        date: date,
        existingEntries: state.data.entries,
        isEditing: true,
        editingEntryId: entryId,
      );

      if (!validation.isValid) {
        throw Exception(validation.errorMessage);
      }

      // Find and update the entry
      final updatedEntries = state.data.entries.map((entry) {
        if (entry.id == entryId) {
          return entry.copyWith(
            weight: weight,
            date: date,
            notes: notes,
            updatedAt: DateTime.now(),
          );
        }
        return entry;
      }).toList();

      // Update data
      final latestEntry = updatedEntries.isEmpty 
          ? null 
          : updatedEntries.reduce((a, b) => a.date.isAfter(b.date) ? a : b);
      
      final chronological = List<WeightEntry>.from(updatedEntries);
      chronological.sort((a, b) => a.date.compareTo(b.date));
      
      final updatedData = state.data.copyWith(
        entries: updatedEntries,
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: chronological.isNotEmpty ? chronological.first.weight : null,
      );

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
      );

      // Save to local storage
      await _saveWeightTrackingData(updatedData);

      // Sync the updated entry to Firestore in background
      final updatedEntry = updatedEntries.firstWhere((e) => e.id == entryId);
      _syncWeightEntryToFirestoreInBackground(updatedEntry);
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في تعديل الوزن: $e',
      );
    }
  }

  /// Delete a weight entry
  Future<void> deleteWeightEntry(String entryId) async {
    state = state.copyWith(isSaving: true, error: null);

    try {
      // Remove the entry
      final updatedEntries = state.data.entries.where((entry) => entry.id != entryId).toList();

      // Update data
      final latestEntry = updatedEntries.isEmpty
          ? null
          : updatedEntries.reduce((a, b) => a.date.isAfter(b.date) ? a : b);

      final chronological = List<WeightEntry>.from(updatedEntries);
      chronological.sort((a, b) => a.date.compareTo(b.date));

      final updatedData = state.data.copyWith(
        entries: updatedEntries,
        lastEntryDate: latestEntry?.date,
        currentWeight: latestEntry?.weight,
        startingWeight: chronological.isNotEmpty ? chronological.first.weight : null,
      );

      state = state.copyWith(
        isSaving: false,
        data: updatedData,
      );

      // Save to local storage
      await _saveWeightTrackingData(updatedData);

      // Delete the entry from Firestore in background
      _deleteWeightEntryFromFirestoreInBackground(entryId);
    } catch (e) {
      state = state.copyWith(
        isSaving: false,
        error: 'فشل في حذف الوزن: $e',
      );
    }
  }

  /// Delete a weight entry from Firestore in background
  void _deleteWeightEntryFromFirestoreInBackground(String entryId) {
    Future.microtask(() async {
      try {
        final syncService = await ref.read(weightTrackingSyncServiceProvider.future);
        await syncService.deleteWeightEntryFromFirestore(entryId);
        AppLogger.info('WeightTrackingNotifier: Successfully deleted weight entry $entryId from Firestore in background');
      } catch (e) {
        AppLogger.warning('WeightTrackingNotifier: Failed to delete weight entry from Firestore in background: $e');
        // Don't throw error as this shouldn't break the UI flow
      }
    });
  }

  /// Update weight tracking settings
  Future<void> updateSettings(WeightTrackingSettings newSettings) async {
    try {
      final updatedData = state.data.copyWith(settings: newSettings);
      state = state.copyWith(data: updatedData);

      // Save settings to local storage
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveWeightSettings(newSettings.toJson());
    } catch (e) {
      state = state.copyWith(error: 'فشل في حفظ الإعدادات: $e');
    }
  }

  /// Save weight tracking data to local storage and sync to Firestore
  Future<void> _saveWeightTrackingData(WeightTrackingData data) async {
    try {
      final localStorageService = await ref.read(localStorageServiceProvider.future);
      await localStorageService.saveWeightTrackingData(data.toJson());

      // Sync to Firestore in background
      _syncToFirestoreInBackground();
    } catch (e) {
      AppLogger.error('WeightTrackingNotifier: Error saving weight tracking data: $e');
    }
  }

  /// Sync weight tracking data to Firestore in background
  void _syncToFirestoreInBackground() {
    Future.microtask(() async {
      try {
        final syncService = await ref.read(weightTrackingSyncServiceProvider.future);
        await syncService.syncWeightTrackingDataToFirestore();
        AppLogger.info('WeightTrackingNotifier: Successfully synced weight tracking data to Firestore in background');
      } catch (e) {
        AppLogger.warning('WeightTrackingNotifier: Failed to sync weight tracking data to Firestore in background: $e');
        // Don't throw error as this shouldn't break the UI flow
      }
    });
  }

  /// Sync a single weight entry to Firestore in background
  void _syncWeightEntryToFirestoreInBackground(WeightEntry entry) {
    Future.microtask(() async {
      try {
        final syncService = await ref.read(weightTrackingSyncServiceProvider.future);
        await syncService.syncWeightEntryToFirestore(entry.toJson());
        AppLogger.info('WeightTrackingNotifier: Successfully synced weight entry ${entry.id} to Firestore in background');
      } catch (e) {
        AppLogger.warning('WeightTrackingNotifier: Failed to sync weight entry to Firestore in background: $e');
        // Don't throw error as this shouldn't break the UI flow
      }
    });
  }

  /// Sync weight tracking data from Firestore (for manual refresh)
  Future<bool> syncFromFirestore() async {
    try {
      AppLogger.info('WeightTrackingNotifier: Syncing weight tracking data from Firestore');

      final syncService = await ref.read(weightTrackingSyncServiceProvider.future);
      final success = await syncService.syncWeightTrackingDataFromFirestore();

      if (success) {
        // Reload data after successful sync
        await _loadWeightTrackingData();
        AppLogger.info('WeightTrackingNotifier: Successfully synced and reloaded weight tracking data from Firestore');
      }

      return success;
    } catch (e) {
      AppLogger.error('WeightTrackingNotifier: Error syncing weight tracking data from Firestore: $e');
      return false;
    }
  }

  /// Clear all weight tracking data from both local storage and Firestore
  Future<void> clearAllData() async {
    try {
      AppLogger.info('WeightTrackingNotifier: Clearing all weight tracking data');

      final syncService = await ref.read(weightTrackingSyncServiceProvider.future);
      await syncService.deleteAllWeightTrackingData();

      state = state.copyWith(data: const WeightTrackingData());
      AppLogger.info('WeightTrackingNotifier: Successfully cleared all weight tracking data');
    } catch (e) {
      AppLogger.error('WeightTrackingNotifier: Error clearing weight tracking data: $e');
      state = state.copyWith(error: 'فشل في مسح البيانات: $e');
    }
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get weight entry by ID
  WeightEntry? getWeightEntry(String entryId) {
    return state.data.entries.firstWhere(
      (entry) => entry.id == entryId,
      orElse: () => throw StateError('Entry not found'),
    );
  }
}

/// Provider for current weight tracking data
@riverpod
WeightTrackingData weightTrackingData(Ref ref) {
  final weightTrackingState = ref.watch(weightTrackingNotifierProvider);
  return weightTrackingState.data;
}

/// Provider for weight tracking settings
@riverpod
WeightTrackingSettings weightTrackingSettings(Ref ref) {
  final weightTrackingState = ref.watch(weightTrackingNotifierProvider);
  return weightTrackingState.data.settings;
}

/// Provider for latest weight entry
@riverpod
WeightEntry? latestWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.latestEntry;
}

/// Provider for checking if new entry can be added
@riverpod
bool canAddNewWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.canAddNewEntry;
}

/// Provider for days until next entry
@riverpod
int daysUntilNextWeightEntry(Ref ref) {
  final weightTrackingData = ref.watch(weightTrackingDataProvider);
  return weightTrackingData.daysUntilNextEntry;
}
