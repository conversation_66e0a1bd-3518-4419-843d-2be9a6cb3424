import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../../core/services/firestore_service.dart';
import '../../../../core/services/local_storage_service.dart';
import '../../../../core/utils/logger.dart';

part 'weight_tracking_sync_service.g.dart';

@riverpod
Future<WeightTrackingSyncService> weightTrackingSyncService(WeightTrackingSyncServiceRef ref) async {
  final firestoreService = ref.read(firestoreServiceProvider);
  final localStorageService = await ref.read(localStorageServiceProvider.future);
  
  return WeightTrackingSyncService(
    firestoreService: firestoreService,
    localStorageService: localStorageService,
  );
}

class WeightTrackingSyncService {
  final FirestoreService _firestoreService;
  final LocalStorageService _localStorageService;

  WeightTrackingSyncService({
    required FirestoreService firestoreService,
    required LocalStorageService localStorageService,
  }) : _firestoreService = firestoreService,
       _localStorageService = localStorageService;

  /// Sync weight tracking data from local storage to Firestore
  Future<bool> syncWeightTrackingDataToFirestore() async {
    try {
      AppLogger.info('WeightTrackingSyncService: Syncing weight tracking data to Firestore');

      // Get data from local storage
      final localData = _localStorageService.loadWeightTrackingData();
      if (localData == null) {
        AppLogger.info('WeightTrackingSyncService: No weight tracking data found in local storage');
        return false;
      }

      // Save to Firestore
      final documentId = await _firestoreService.saveWeightTrackingData(localData);

      AppLogger.info('WeightTrackingSyncService: Successfully synced weight tracking data to Firestore with document ID: $documentId');
      return true;
    } catch (e) {
      AppLogger.error('WeightTrackingSyncService: Error syncing weight tracking data to Firestore: $e');
      return false;
    }
  }

  /// Sync weight tracking data from Firestore to local storage
  Future<bool> syncWeightTrackingDataFromFirestore() async {
    try {
      AppLogger.info('WeightTrackingSyncService: Syncing weight tracking data from Firestore');

      // Get data from Firestore
      final firestoreData = await _firestoreService.getWeightTrackingData();
      if (firestoreData == null) {
        AppLogger.info('WeightTrackingSyncService: No weight tracking data found in Firestore');
        return false;
      }

      // Remove Firestore metadata before saving to local storage
      final cleanedData = Map<String, dynamic>.from(firestoreData);
      cleanedData.remove('document_id');
      cleanedData.remove('synced_at');
      cleanedData.remove('last_modified');

      // Save to local storage
      final success = await _localStorageService.saveWeightTrackingData(cleanedData);
      
      if (success) {
        AppLogger.info('WeightTrackingSyncService: Successfully synced weight tracking data from Firestore to local storage');
        return true;
      } else {
        AppLogger.error('WeightTrackingSyncService: Failed to save weight tracking data to local storage');
        return false;
      }
    } catch (e) {
      AppLogger.error('WeightTrackingSyncService: Error syncing weight tracking data from Firestore: $e');
      return false;
    }
  }

  /// Bidirectional sync - prioritizes Firestore data if available, otherwise syncs local data to Firestore
  Future<bool> bidirectionalSync() async {
    try {
      AppLogger.info('WeightTrackingSyncService: Starting bidirectional sync');

      // First, try to sync from Firestore to local storage
      final syncFromFirestoreSuccess = await syncWeightTrackingDataFromFirestore();
      
      if (syncFromFirestoreSuccess) {
        AppLogger.info('WeightTrackingSyncService: Bidirectional sync completed - used Firestore data');
        return true;
      }

      // If no Firestore data, try to sync local data to Firestore
      final syncToFirestoreSuccess = await syncWeightTrackingDataToFirestore();
      
      if (syncToFirestoreSuccess) {
        AppLogger.info('WeightTrackingSyncService: Bidirectional sync completed - synced local data to Firestore');
        return true;
      }

      AppLogger.info('WeightTrackingSyncService: Bidirectional sync completed - no data to sync');
      return false;
    } catch (e) {
      AppLogger.error('WeightTrackingSyncService: Error during bidirectional sync: $e');
      return false;
    }
  }

  /// Delete weight tracking data from both local storage and Firestore
  Future<bool> deleteAllWeightTrackingData() async {
    try {
      AppLogger.info('WeightTrackingSyncService: Deleting all weight tracking data');

      // Delete from Firestore
      final firestoreDeleteSuccess = await _firestoreService.deleteWeightTrackingData();
      
      // Delete from local storage
      final localDeleteSuccess = await _localStorageService.clearAllWeightTrackingData();

      final success = firestoreDeleteSuccess && localDeleteSuccess;
      
      if (success) {
        AppLogger.info('WeightTrackingSyncService: Successfully deleted all weight tracking data');
      } else {
        AppLogger.warning('WeightTrackingSyncService: Partial deletion - Firestore: $firestoreDeleteSuccess, Local: $localDeleteSuccess');
      }
      
      return success;
    } catch (e) {
      AppLogger.error('WeightTrackingSyncService: Error deleting weight tracking data: $e');
      return false;
    }
  }
}
